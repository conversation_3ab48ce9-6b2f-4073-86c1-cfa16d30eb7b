import Header from '../components/Header'
import ContactForm from '../components/ContactForm'
import Footer from '../components/Footer'

type Project = {
  id: number;
  title: string;
  client: string;
  area: string;
  image: string;
  category: string;
};

type Industry = {
  id: number;
  name: string;
  image: string;
};

export default function Projects() {
  const projects: Project[] = [
    {
      id: 1,
      title: "Tech Expo 2024",
      client: "TechCorp",
      area: "120 m²",
      image: "",
      category: "Technology"
    },
    {
      id: 2,
      title: "Healthcare Summit",
      client: "MedLife",
      area: "85 m²",
      image: "",
      category: "Healthcare"
    },
    {
      id: 3,
      title: "Auto Show Dubai",
      client: "AutoMax",
      area: "200 m²",
      image: "",
      category: "Automotive"
    },
    {
      id: 4,
      title: "Fashion Week",
      client: "StyleHub",
      area: "150 m²",
      image: "",
      category: "Fashion"
    },
    {
      id: 5,
      title: "Food & Beverage Expo",
      client: "FoodCorp",
      area: "95 m²",
      image: "",
      category: "Food & Beverage"
    },
    {
      id: 6,
      title: "Real Estate Fair",
      client: "PropTech",
      area: "180 m²",
      image: "",
      category: "Real Estate"
    },
    {
      id: 7,
      title: "Energy Conference",
      client: "GreenPower",
      area: "110 m²",
      image: "",
      category: "Energy"
    },
    {
      id: 8,
      title: "Education Expo",
      client: "EduTech",
      area: "75 m²",
      image: "",
      category: "Education"
    },
    {
      id: 9,
      title: "Finance Summit",
      client: "FinanceHub",
      area: "130 m²",
      image: "",
      category: "Finance"
    }
  ];

  const industries: Industry[] = [
    { id: 1, name: "Technology", image: "" },
    { id: 2, name: "Healthcare", image: "" },
    { id: 3, name: "Automotive", image: "" },
    { id: 4, name: "Fashion", image: "" },
    { id: 5, name: "Food & Beverage", image: "" },
    { id: 6, name: "Real Estate", image: "" },
    { id: 7, name: "Energy", image: "" },
    { id: 8, name: "Education", image: "" }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Projects Gallery Section */}
      <section className="py-16 bg-white">
        <div className="max-w-[1360px] mx-auto px-4">
          <h1 className="text-4xl lg:text-5xl font-bold text-black text-center mb-16">
            Our Projects
          </h1>
          
          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {projects.map((project) => (
              <div key={project.id} className="group cursor-pointer">
                {/* Project Image */}
                <div className="bg-[#D9D9D9] rounded-lg h-64 mb-4 overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                    <span className="text-lg font-bold text-gray-600">Project Image</span>
                  </div>
                </div>
                
                {/* Project Info */}
                <div className="space-y-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-xl font-bold text-black group-hover:text-[#656CAF] transition-colors">
                        {project.title}
                      </h3>
                      <p className="text-lg font-medium text-black">{project.client}</p>
                      <p className="text-sm text-gray-600">{project.category}</p>
                    </div>
                    <span className="text-lg font-bold text-[#656CAF]">{project.area}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industries Section */}
      <section className="py-16 bg-[#E9E9E9]">
        <div className="max-w-[1360px] mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-bold text-black mb-2">
            Industries:
          </h2>
          
          {/* Industries Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
            {industries.map((industry) => (
              <div key={industry.id} className="group cursor-pointer">
                {/* Industry Image */}
                <div className="bg-white rounded-lg h-24 mb-3 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                  <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-500">Icon</span>
                  </div>
                </div>
                
                {/* Industry Name */}
                <p className="text-sm font-medium text-black text-center group-hover:text-[#656CAF] transition-colors">
                  {industry.name}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <ContactForm />
      <Footer />
    </div>
  );
}
