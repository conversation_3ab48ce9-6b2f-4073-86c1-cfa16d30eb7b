import Header from '../components/Header'
import ContactForm from '../components/ContactForm'
import Footer from '../components/Footer'

type Service = {
  id: number;
  title: string;
  description: string;
};

export default function AboutUs() {
  const services: Service[] = [
    {
      id: 1,
      title: "Exhibition Stand Design",
      description: "Custom exhibition stand designs that reflect your brand identity and attract visitors."
    },
    {
      id: 2,
      title: "Construction & Installation",
      description: "Full in-house production with European methods and materials for precise execution."
    },
    {
      id: 3,
      title: "Project Management",
      description: "Dedicated project managers ensuring smooth communication and efficient decision-making."
    },
    {
      id: 4,
      title: "Pavilion Design",
      description: "Large-scale pavilion designs for major exhibitions and trade shows."
    },
    {
      id: 5,
      title: "Retail Spaces",
      description: "Point-of-sale solutions and retail space design with expert guidance."
    },
    {
      id: 6,
      title: "Bespoke Exhibits",
      description: "Custom exhibits tailored to your specific requirements and objectives."
    }
  ];

  const timelineEvents = [
    { year: "2010", event: "Company Founded" },
    { year: "2015", event: "European Expansion" },
    { year: "2018", event: "Dubai Office Opening" },
    { year: "2020", event: "Digital Innovation" },
    { year: "2023", event: "Sustainability Focus" },
    { year: "2024", event: "AI Integration" }
  ];

  return (
    <div className="min-h-screen bg-[#E9E9E9]">
      <Header />
      
      {/* About Us Hero Section */}
      <section className="py-16 bg-[#E9E9E9]">
        <div className="max-w-[1360px] mx-auto px-4">
          <h1 className="text-4xl lg:text-5xl font-bold text-black text-center mb-8">
            About Us
          </h1>
          
          <div className="max-w-4xl mx-auto text-center">
            <p className="text-xl font-bold text-black leading-relaxed mb-8">
              At messe.ae, we specialize in exhibition stand design that reflects our clients' brand and goals. 
              Using the latest European techniques and materials, we craft high-quality designs for exhibitions, 
              pavilions, and retail spaces. As a trusted builder in Dubai, we offer end-to-end solutions to bring 
              your vision to life.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 bg-white">
        <div className="max-w-[1360px] mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-bold text-black mb-12">
            Our story
          </h2>
          
          <div className="max-w-4xl">
            <p className="text-xl font-bold text-black leading-relaxed">
              The clarity of our position is obvious: a deep level of immersion plays a crucial role in the 
              development model. Cartel arrangements do not allow a situation in which supporters of totalitarianism 
              in science, who are a vivid example of the continental European type of political culture, will be 
              exposed. It should be noted that the further development of various forms of activity contributes to 
              the preparation and implementation of new proposals.
            </p>
          </div>
        </div>
      </section>

      {/* Our Services Section */}
      <section className="py-16 bg-[#E9E9E9]">
        <div className="max-w-[1360px] mx-auto px-4">
          <h2 className="text-3xl lg:text-4xl font-bold text-black mb-12">
            Our Services
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {services.map((service) => (
              <div key={service.id} className="bg-[#EAEAEA] rounded-lg p-6">
                <h3 className="text-xl font-bold text-black mb-4">{service.title}</h3>
                <p className="text-lg text-black leading-relaxed">{service.description}</p>
              </div>
            ))}
          </div>

          <div className="max-w-4xl">
            <p className="text-xl font-bold text-black leading-relaxed mb-8">
              We ensure high-quality construction and installation of exhibition stands with full in-house production, 
              overseeing every stage for precise execution. Using advanced European methods and materials, we craft each 
              stand with precision. Unlike many companies, we do not outsource the building process, maintaining full 
              quality control for exceptional results.
            </p>
            
            <p className="text-xl font-bold text-black leading-relaxed">
              Our success in expo stand design, construction, and installation comes from a seamless, well-structured 
              process. With project management at the core, each client gets a dedicated manager ensuring smooth 
              communication and efficient decision-making. From bespoke exhibits to point-of-sale solutions, we provide 
              expert guidance and full support at every stage.
            </p>
          </div>
        </div>
      </section>

      {/* Expoglobal Group Timeline */}
      <section className="py-16 bg-white">
        <div className="max-w-[1360px] mx-auto px-4">
          <h2 className="text-2xl lg:text-3xl font-bold text-black mb-12">
            Expoglobal Group
          </h2>
          
          {/* Timeline */}
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-black"></div>
            
            <div className="space-y-8">
              {timelineEvents.map((event, index) => (
                <div key={index} className="relative flex items-center">
                  {/* Timeline dot */}
                  <div className="absolute left-2 w-4 h-4 bg-black rounded-full transform -translate-x-1/2"></div>
                  
                  {/* Content */}
                  <div className="ml-12">
                    <div className="text-xl font-bold text-black">{event.year}</div>
                    <div className="text-lg text-black">{event.event}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <ContactForm />
      <Footer />
    </div>
  );
}
