import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

type HeaderProps = {
  className?: string;
};

export default function Header({ className = '' }: HeaderProps) {
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <header className={`bg-white py-4 relative ${className}`}>
      <div className="max-w-[1360px] mx-auto px-4 flex items-center justify-between">
        {/* Logo */}
        <Link to="/" className="bg-[#E9E9E9] rounded-lg px-6 py-3 hover:bg-opacity-80 transition-colors">
          <span className="text-2xl font-bold text-black">logo</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden lg:flex items-center space-x-8">
          <Link
            to="/projects"
            className={`text-lg lg:text-2xl font-bold transition-colors ${
              location.pathname === '/projects' ? 'text-[#656CAF]' : 'text-black hover:text-[#656CAF]'
            }`}
          >
            Projects
          </Link>
          <Link
            to="/about"
            className={`text-lg lg:text-2xl font-bold transition-colors ${
              location.pathname === '/about' ? 'text-[#656CAF]' : 'text-black hover:text-[#656CAF]'
            }`}
          >
            About Us
          </Link>
          <a href="tel:+97145485887" className="text-lg lg:text-2xl font-bold text-black hover:text-[#656CAF] transition-colors">
            +971 4 548 5887
          </a>
        </nav>

        {/* Header Image - Hidden on mobile when menu is open */}
        <div className={`w-12 h-12 bg-[#D9D9D9] rounded ${isMenuOpen ? 'hidden' : 'block'} lg:block`}></div>

        {/* Mobile menu button */}
        <button
          className="lg:hidden p-2 z-50 relative"
          onClick={toggleMenu}
          aria-label="Toggle mobile menu"
        >
          <svg
            className={`w-6 h-6 transition-transform duration-300 ${isMenuOpen ? 'rotate-90' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {isMenuOpen ? (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
      </div>

      {/* Mobile Navigation Menu */}
      <div className={`lg:hidden absolute top-full left-0 right-0 bg-white shadow-lg transition-all duration-300 ease-in-out z-40 ${
        isMenuOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-4'
      }`}>
        <nav className="px-4 py-6 space-y-4">
          <Link
            to="/projects"
            onClick={closeMenu}
            className={`block text-xl font-bold transition-colors py-2 ${
              location.pathname === '/projects' ? 'text-[#656CAF]' : 'text-black hover:text-[#656CAF]'
            }`}
          >
            Projects
          </Link>
          <Link
            to="/about"
            onClick={closeMenu}
            className={`block text-xl font-bold transition-colors py-2 ${
              location.pathname === '/about' ? 'text-[#656CAF]' : 'text-black hover:text-[#656CAF]'
            }`}
          >
            About Us
          </Link>
          <a
            href="tel:+97145485887"
            onClick={closeMenu}
            className="block text-xl font-bold text-black hover:text-[#656CAF] transition-colors py-2"
          >
            +971 4 548 5887
          </a>
        </nav>
      </div>

      {/* Mobile menu overlay */}
      {isMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-25 z-30"
          onClick={closeMenu}
        ></div>
      )}
    </header>
  );
}
