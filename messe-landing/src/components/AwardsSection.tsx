type Award = {
  id: number;
  category: string;
  show: string;
  client: string;
  article: string;
  image: string;
};

type AwardsSectionProps = {
  className?: string;
};

export default function AwardsSection({ className = '' }: AwardsSectionProps) {
  const awards: Award[] = [
    {
      id: 1,
      category: "Best Pavilion",
      show: "Big 5",
      client: "Belgium Pavilion",
      article: "World Exhibition Stand Awards – The Winners Supplement",
      image: "/images/award-1.png"
    },
    {
      id: 2,
      category: "Double-Deck Exhibit",
      show: "Interplastica",
      client: "Sibur Holding PJSC",
      article: "Exhibitor Magazine",
      image: "/images/award-2.png"
    },
    {
      id: 3,
      category: "Best Sustainable Stand",
      show: "ADIPEC",
      client: "Siemens Energy",
      article: "World Exhibition Stand Awards – The Winners Supplement",
      image: "/images/award-3.png"
    },
    {
      id: 4,
      category: "International Exhibit",
      show: "Dubai International Boat show",
      client: "Amels",
      article: "Exhibitor Magazine",
      image: "/images/award-4.png"
    }
  ];

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Awards Grid */}
        <div className="inline-flex justify-start items-start gap-8 overflow-hidden">
          {awards.map((award) => (
            <div key={award.id} data-use="Web" className="w-80 inline-flex flex-col justify-start items-start gap-5 overflow-hidden">
              {/* Award Image */}
              <img
                className="w-60 h-96 relative object-cover"
                src={award.image}
                alt={award.category}
              />

              {/* Award Info */}
              <div className="self-stretch flex flex-col justify-start items-start gap-1">
                <div className="self-stretch inline-flex justify-start items-center gap-1">
                  <div className="justify-start text-[#262626] text-sm font-bold font-['Roboto'] leading-none tracking-tight">Category:</div>
                  <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">{award.category}</div>
                </div>

                <div className="self-stretch inline-flex justify-start items-center gap-1">
                  <div className="justify-start text-[#262626] text-sm font-bold font-['Roboto'] leading-none tracking-tight">Show:</div>
                  <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">{award.show}</div>
                </div>

                <div className="self-stretch inline-flex justify-start items-center gap-1">
                  <div className="justify-start text-[#262626] text-sm font-bold font-['Roboto'] leading-none tracking-tight">Client:</div>
                  <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">{award.client}</div>
                </div>

                <div className="self-stretch inline-flex justify-start items-center gap-1">
                  <div className="justify-start text-[#262626] text-sm font-bold font-['Roboto'] leading-none tracking-tight">Article</div>
                  <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">at</div>
                  <div className="w-44 justify-start text-[#656CAF] text-sm font-bold font-['Roboto'] underline leading-none tracking-tight cursor-pointer hover:text-[#4C53A2] transition-colors">
                    {award.article}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
