type Award = {
  id: number;
  title: string;
  description: string;
  image: string;
  year: string;
};

type AwardsSectionProps = {
  className?: string;
};

export default function AwardsSection({ className = '' }: AwardsSectionProps) {
  const awards: Award[] = [
    {
      id: 1,
      title: "Best Exhibition Stand Design",
      description: "Awarded for innovative and creative exhibition stand design that exceeded client expectations.",
      image: "/images/award-1.png",
      year: "2023"
    },
    {
      id: 2,
      title: "Excellence in Event Management",
      description: "Recognition for outstanding project management and flawless execution of complex exhibitions.",
      image: "/images/award-2.png",
      year: "2022"
    },
    {
      id: 3,
      title: "Innovation in Display Technology",
      description: "Honored for integrating cutting-edge technology into traditional exhibition displays.",
      image: "/images/award-3.png",
      year: "2022"
    },
    {
      id: 4,
      title: "Sustainability in Exhibition Design",
      description: "Recognized for commitment to eco-friendly materials and sustainable exhibition practices.",
      image: "/images/award-4.png",
      year: "2021"
    }
  ];

  return (
    <section className={`py-16 bg-[#F5F5F5] ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[#262626] mb-6">
            We proved our expertise by achieving significant awards
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our commitment to excellence has been recognized by industry leaders and clients worldwide
          </p>
        </div>

        {/* Awards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {awards.map((award) => (
            <div key={award.id} className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
              {/* Award Image */}
              <div className="mb-6">
                <img 
                  src={award.image} 
                  alt={award.title}
                  className="w-full h-32 object-contain"
                />
              </div>
              
              {/* Award Info */}
              <div className="space-y-4">
                <div className="text-right">
                  <span className="text-sm font-bold text-[#656CAF] bg-[#656CAF]/10 px-3 py-1 rounded-full">
                    {award.year}
                  </span>
                </div>
                
                <h3 className="text-xl font-bold text-[#262626]">
                  {award.title}
                </h3>
                
                <p className="text-gray-600 text-sm leading-relaxed">
                  {award.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
