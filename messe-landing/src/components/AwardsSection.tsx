type Award = {
  id: number;
  category: string;
  show: string;
  client: string;
  article: string;
  image: string;
};

type AwardsSectionProps = {
  className?: string;
};

export default function AwardsSection({ className = '' }: AwardsSectionProps) {
  const awards: Award[] = [
    {
      id: 1,
      category: "Best Pavilion",
      show: "Big 5",
      client: "Belgium Pavilion",
      article: "World Exhibition Stand Awards – The Winners Supplement",
      image: "/images/award-1.png"
    },
    {
      id: 2,
      category: "Double-Deck Exhibit",
      show: "Interplastica",
      client: "Sibur Holding PJSC",
      article: "Exhibitor Magazine",
      image: "/images/award-2.png"
    },
    {
      id: 3,
      category: "Best Sustainable Stand",
      show: "ADIPEC",
      client: "Siemens Energy",
      article: "World Exhibition Stand Awards – The Winners Supplement",
      image: "/images/award-3.png"
    },
    {
      id: 4,
      category: "International Exhibit",
      show: "Dubai International Boat show",
      client: "Amels",
      article: "Exhibitor Magazine",
      image: "/images/award-4.png"
    }
  ];

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Awards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {awards.map((award) => (
            <div key={award.id} className="flex flex-col gap-5">
              {/* Award Image */}
              <div className="h-48">
                <img
                  src={award.image}
                  alt={award.category}
                  className="w-full h-full object-cover rounded"
                />
              </div>

              {/* Award Info */}
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <span className="text-sm font-bold text-black">Category:</span>
                  <span className="text-sm text-black">{award.category}</span>
                </div>

                <div className="flex items-center gap-1">
                  <span className="text-sm font-bold text-black">Show:</span>
                  <span className="text-sm text-black">{award.show}</span>
                </div>

                <div className="flex items-center gap-1">
                  <span className="text-sm font-bold text-black">Client:</span>
                  <span className="text-sm text-black">{award.client}</span>
                </div>

                <div className="flex items-center gap-1">
                  <span className="text-sm font-bold text-[#262626]">Article</span>
                  <span className="text-sm text-[#262626]">at</span>
                  <span className="text-sm font-bold text-[#656CAF] underline cursor-pointer hover:text-[#4C53A2]">
                    {award.article}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
