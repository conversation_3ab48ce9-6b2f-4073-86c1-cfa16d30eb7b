type HeroSectionProps = {
  className?: string;
};

export default function HeroSection({ className = '' }: HeroSectionProps) {
  return (
    <section className={`relative min-h-screen flex items-center justify-center overflow-hidden ${className}`}>
      {/* Background Image with Gradient Overlay */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-white"></div>
        <div
          className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black"
          style={{
            background: `
              linear-gradient(180deg, rgba(0,0,0,0) 30%, rgba(0,0,0,1) 84%),
              url('/images/parallax-bg.png')
            `,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        ></div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 text-center text-white">
        {/* Main Heading */}
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
          Exhibition Stand Builder & Designer<br />
          in UAE and around the world
        </h1>

        {/* Years Badge */}
        <div className="mb-6">
          <div className="inline-flex items-baseline gap-2">
            <span className="text-6xl md:text-7xl lg:text-8xl font-bold">20</span>
            <span className="text-2xl md:text-3xl lg:text-4xl font-normal">
              years of award winning expertise
            </span>
          </div>
        </div>

        {/* Description */}
        <p className="text-xl md:text-2xl lg:text-3xl font-normal mb-8 max-w-4xl mx-auto leading-relaxed">
          Your great exhibition stand design starts here.<br />
          Fill in the form, and we will handle the rest.
        </p>

        {/* CTA Button */}
        <button className="bg-[#656CAF] text-white font-bold text-lg lg:text-xl px-8 lg:px-10 py-4 lg:py-5 rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-lg">
          Discuss Your Project
        </button>
      </div>
    </section>
  );
}
