type PartnersSectionProps = {
  className?: string;
};

export default function PartnersSection({ className = '' }: PartnersSectionProps) {
  // Expoglobal logos data
  const expoglobalLogos = [
    { id: 1, src: '/images/expoglobal-logo-1.svg', alt: 'Expoglobal Logo 1' },
    { id: 2, src: '/images/expoglobal-logo-2.svg', alt: 'Expoglobal Logo 2' },
    { id: 3, src: '/images/expoglobal-logo-3.svg', alt: 'Expoglobal Logo 3' },
    { id: 4, src: '/images/expoglobal-logo-4.svg', alt: 'Expoglobal Logo 4' },
    { id: 5, src: '/images/expoglobal-logo-5.svg', alt: 'Expoglobal Logo 5' },
    { id: 6, src: '/images/expoglobal-logo-6.svg', alt: 'Expoglobal Logo 6' },
    { id: 7, src: '/images/expoglobal-logo-7.svg', alt: 'Expoglobal Logo 7' },
  ];

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Logos */}
          <div>
            <div className="grid grid-cols-3 gap-8 items-center justify-items-center">
              {expoglobalLogos.map((logo) => (
                <div
                  key={logo.id}
                  className="w-20 h-20 lg:w-24 lg:h-24 flex items-center justify-center hover:scale-105 transition-transform duration-200"
                >
                  <img
                    src={logo.src}
                    alt={logo.alt}
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Right Column - Content */}
          <div className="space-y-6">
            <h2 className="text-3xl lg:text-4xl font-bold text-[#262626]">
              Part of Expoglobal Group
            </h2>

            <p className="text-lg text-gray-600 leading-relaxed">
              We are proud to be part of the Expoglobal Group, a leading network of exhibition
              and event professionals across the globe. This partnership allows us to deliver
              world-class exhibition solutions with international expertise and local knowledge.
            </p>

            <p className="text-lg text-gray-600 leading-relaxed">
              Our collaboration with Expoglobal ensures that your exhibition stands meet the
              highest international standards while being perfectly tailored to the UAE market.
            </p>

            {/* CTA Button */}
            <button className="bg-[#656CAF] text-white font-bold text-lg px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-lg">
              Learn More About Our Partnership
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
