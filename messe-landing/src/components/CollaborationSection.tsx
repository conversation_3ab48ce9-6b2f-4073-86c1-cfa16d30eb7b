type CollaborationSectionProps = {
  className?: string;
};

export default function CollaborationSection({ className = '' }: CollaborationSectionProps) {
  return (
    <section className={`py-16 bg-[#E9E9E9] ${className}`}>
      <div className="max-w-[1360px] mx-auto px-4">
        {/* Section Title */}
        <h2 className="text-4xl font-bold text-black text-center mb-16">
          Our Collaboration Process
        </h2>

        {/* Process Image/Placeholder */}
        <div className="bg-[#D9D9D9] rounded-lg h-64 mb-8 flex items-center justify-center">
          <span className="text-2xl font-bold text-black">Process Visualization</span>
        </div>
      </div>
    </section>
  );
}
