type ProjectCategory = {
  id: number;
  size: string;
  image: string;
};

import { Link } from 'react-router-dom';

type ProjectsSectionProps = {
  className?: string;
};

export default function ProjectsSection({ className = '' }: ProjectsSectionProps) {
  const projectCategories: ProjectCategory[] = [
    {
      id: 1,
      size: "< 100 m2",
      image: "/images/project-1.png"
    },
    {
      id: 2,
      size: "100 m - 300 m",
      image: "/images/project-2.png"
    },
    {
      id: 3,
      size: "> 300 m",
      image: "/images/project-3.png"
    },
    {
      id: 4,
      size: "Double-deckers",
      image: "/images/project-4.png"
    },
  ];

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {projectCategories.map((category) => (
            <div key={category.id} className="group cursor-pointer relative">
              {/* Project Category Card */}
              <div
                className="relative overflow-hidden rounded h-80 flex items-end"
                style={{
                  backgroundImage: `
                    linear-gradient(180deg, rgba(0,0,0,1) 10%, rgba(0,0,0,0) 28%),
                    url('${category.image}')
                  `,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat'
                }}
              >
                {/* Navigation Arrows */}
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                  <button className="w-8 h-8 flex items-center justify-center text-white hover:bg-white hover:bg-opacity-20 rounded transition-all">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="4" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                </div>

                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <button className="w-8 h-8 flex items-center justify-center text-white hover:bg-white hover:bg-opacity-20 rounded transition-all">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" strokeWidth="4" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>

                {/* Size Label */}
                <div className="p-6 w-full">
                  <h3 className="text-2xl lg:text-3xl font-bold text-white">
                    {category.size}
                  </h3>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Description and CTA */}
        <div className="text-center">
          <p className="text-2xl lg:text-3xl text-[#262626] mb-8 max-w-4xl mx-auto leading-relaxed">
            Take the first step towards exhibition success.<br />
            Let's start planning your standout exhibition experience
          </p>

          {/* CTA Button */}
          <button className="bg-[#656CAF] text-white font-normal text-xl lg:text-2xl px-6 lg:px-8 py-3 lg:py-4 rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-lg">
            Plan your perfect stand
          </button>
        </div>
      </div>
    </section>
  );
}
