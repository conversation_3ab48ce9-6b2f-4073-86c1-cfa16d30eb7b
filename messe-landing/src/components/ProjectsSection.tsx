type Project = {
  id: number;
  title: string;
  client: string;
  area: string;
  image: string;
};

import { Link } from 'react-router-dom';

type ProjectsSectionProps = {
  className?: string;
};

export default function ProjectsSection({ className = '' }: ProjectsSectionProps) {
  const projects: Project[] = [
    {
      id: 1,
      title: "Exhibition Stand Design",
      client: "Technology Expo",
      area: "120 m²",
      image: "/images/project-1.png"
    },
    {
      id: 2,
      title: "Trade Show Booth",
      client: "Healthcare Summit",
      area: "85 m²",
      image: "/images/project-2.png"
    },
    {
      id: 3,
      title: "Corporate Exhibition",
      client: "Energy Conference",
      area: "200 m²",
      image: "/images/project-3.png"
    },
    {
      id: 4,
      title: "Interactive Display",
      client: "Innovation Fair",
      area: "150 m²",
      image: "/images/project-4.png"
    },
  ];

  return (
    <section className={`py-16 bg-[#F5F5F5] ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Title */}
        <h2 className="text-4xl lg:text-5xl font-bold text-[#262626] text-center mb-16">
          Our Projects
        </h2>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {projects.map((project) => (
            <div key={project.id} className="group cursor-pointer">
              {/* Project Image */}
              <div className="relative overflow-hidden rounded-lg mb-4 h-64">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
              </div>

              {/* Project Info */}
              <div className="space-y-2">
                <div className="text-right">
                  <span className="text-lg font-bold text-[#656CAF]">{project.area}</span>
                </div>
                <div>
                  <p className="text-lg text-gray-600">{project.client}</p>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-[#262626]">{project.title}</h3>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Description and CTA */}
        <div className="text-center">
          <p className="text-xl lg:text-2xl text-[#262626] mb-8 max-w-4xl mx-auto">
            Take the first step towards exhibition success. Let's start planning your standout exhibition experience
          </p>

          {/* All Projects Button */}
          <Link
            to="/projects"
            className="inline-block bg-[#656CAF] text-white font-bold text-xl px-10 py-5 rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-lg"
          >
            All Projects
          </Link>
        </div>
      </div>
    </section>
  );
}
