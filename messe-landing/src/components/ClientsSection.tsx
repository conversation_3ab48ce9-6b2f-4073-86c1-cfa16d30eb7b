type ClientsSectionProps = {
  className?: string;
};

export default function ClientsSection({ className = '' }: ClientsSectionProps) {
  // Client logos data
  const clientLogos = Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    src: `/images/clients/client-${i + 1}.png`,
    alt: `Client ${i + 1} logo`
  }));

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-3xl lg:text-4xl font-bold text-[#262626] text-center mb-12">
          We build partnerships that always come back
        </h2>

        {/* First Row - 10 logos */}
        <div className="flex items-center justify-center gap-8 lg:gap-12 mb-8 overflow-hidden">
          <div className="flex items-center gap-8 lg:gap-12">
            {clientLogos.slice(0, 10).map((logo) => (
              <div key={logo.id} className="flex-shrink-0 w-16 h-16 lg:w-20 lg:h-20 flex items-center justify-center">
                <img
                  src={logo.src}
                  alt={logo.alt}
                  className="max-w-full max-h-full object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Second Row - 10 logos */}
        <div className="flex items-center justify-center gap-8 lg:gap-12 overflow-hidden">
          <div className="flex items-center gap-8 lg:gap-12">
            {clientLogos.slice(10, 20).map((logo) => (
              <div key={logo.id} className="flex-shrink-0 w-16 h-16 lg:w-20 lg:h-20 flex items-center justify-center">
                <img
                  src={logo.src}
                  alt={logo.alt}
                  className="max-w-full max-h-full object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
