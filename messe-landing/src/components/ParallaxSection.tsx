type ParallaxSectionProps = {
  className?: string;
};

export default function ParallaxSection({ className = '' }: ParallaxSectionProps) {
  return (
    <section className={`relative py-32 overflow-hidden ${className}`}>
      {/* Background Image with Parallax Effect */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          background: `
            linear-gradient(180deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.8) 100%),
            url('/images/parallax-bg.png')
          `,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      ></div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 text-center text-white">
        <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8">
          Why messe.ae only?
        </h2>
        
        <p className="text-xl md:text-2xl lg:text-3xl font-light max-w-4xl mx-auto leading-relaxed">
          We combine 20 years of award-winning expertise with innovative design 
          and flawless execution to create exhibition stands that truly stand out.
        </p>
      </div>
    </section>
  );
}
