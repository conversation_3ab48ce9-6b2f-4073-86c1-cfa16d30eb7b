type Advantage = {
  id: number;
  title: string;
  description: string;
  image: string;
};

import { Link } from 'react-router-dom';

type AdvantagesSectionProps = {
  className?: string;
};

export default function AdvantagesSection({ className = '' }: AdvantagesSectionProps) {
  const advantages: Advantage[] = [
    {
      id: 1,
      title: "Expert Design Team",
      description: "Our award-winning designers bring 20 years of experience to create stunning exhibition stands that capture attention and drive engagement.",
      image: "/images/project-1.png"
    },
    {
      id: 2,
      title: "Full-Service Solutions",
      description: "From concept to completion, we handle every aspect of your exhibition project including design, construction, logistics, and on-site support.",
      image: "/images/project-2.png"
    },
    {
      id: 3,
      title: "International Standards",
      description: "As part of Expoglobal Group, we deliver world-class exhibition solutions that meet international standards while understanding local requirements.",
      image: "/images/project-3.png"
    },
    {
      id: 4,
      title: "Proven Track Record",
      description: "With over 150 successful projects completed, we have established ourselves as the leading exhibition stand builder in UAE and the region.",
      image: "/images/project-4.png"
    },
  ];



  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Advantages Grid */}
        <div className="inline-flex justify-start items-start gap-8 overflow-hidden">
          {advantages.map((advantage) => (
            <div key={advantage.id} data-use="Web" className="w-80 inline-flex flex-col justify-start items-start gap-5 overflow-hidden">
              {/* Advantage Image */}
              <img
                className="w-60 h-96 relative object-cover"
                src={advantage.image}
                alt={advantage.title}
              />

              {/* Advantage Info */}
              <div className="self-stretch flex flex-col justify-start items-start gap-1">
                <div className="self-stretch inline-flex justify-start items-center gap-1">
                  <div className="justify-start text-[#262626] text-sm font-bold font-['Roboto'] leading-none tracking-tight">Title:</div>
                  <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">{advantage.title}</div>
                </div>

                <div className="self-stretch inline-flex justify-start items-start gap-1">
                  <div className="justify-start text-[#262626] text-sm font-bold font-['Roboto'] leading-none tracking-tight">Description:</div>
                  <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight flex-1">
                    {advantage.description}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
