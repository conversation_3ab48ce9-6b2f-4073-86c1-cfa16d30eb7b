type Advantage = {
  id: number;
  title: string;
  description: string;
  icon: string;
};

import { Link } from 'react-router-dom';

type AdvantagesSectionProps = {
  className?: string;
};

export default function AdvantagesSection({ className = '' }: AdvantagesSectionProps) {
  const advantages: Advantage[] = [
    {
      id: 1,
      title: "Expert Design Team",
      description: "Our award-winning designers bring 20 years of experience to create stunning exhibition stands that capture attention and drive engagement.",
      icon: "design"
    },
    {
      id: 2,
      title: "Full-Service Solutions",
      description: "From concept to completion, we handle every aspect of your exhibition project including design, construction, logistics, and on-site support.",
      icon: "service"
    },
    {
      id: 3,
      title: "International Standards",
      description: "As part of Expoglobal Group, we deliver world-class exhibition solutions that meet international standards while understanding local requirements.",
      icon: "global"
    },
    {
      id: 4,
      title: "Proven Track Record",
      description: "With over 150 successful projects completed, we have established ourselves as the leading exhibition stand builder in UAE and the region.",
      icon: "success"
    },
  ];

  // Icon component for advantages
  const getIcon = (iconType: string) => {
    switch (iconType) {
      case 'design':
        return (
          <svg className="w-8 h-8 text-[#656CAF]" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        );
      case 'service':
        return (
          <svg className="w-8 h-8 text-[#656CAF]" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        );
      case 'global':
        return (
          <svg className="w-8 h-8 text-[#656CAF]" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
          </svg>
        );
      case 'success':
        return (
          <svg className="w-8 h-8 text-[#656CAF]" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19V1h-2v1H7V1H5v1H4.5C3.11 2 2 3.11 2 4.5v15C2 20.89 3.11 22 4.5 22h15c1.39 0 2.5-1.11 2.5-2.5v-15C22 3.11 20.89 2 19.5 2zM20 19.5c0 .28-.22.5-.5.5h-15c-.28 0-.5-.22-.5-.5v-15c0-.28.22-.5.5-.5h15c.28 0 .5.22.5.5v15z"/>
          </svg>
        );
      default:
        return (
          <svg className="w-8 h-8 text-[#656CAF]" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
        );
    }
  };

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[#262626] mb-6">
            Why Choose messe.ae?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We deliver exceptional exhibition experiences through expertise, innovation, and unwavering commitment to excellence
          </p>
        </div>

        {/* Advantages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {advantages.map((advantage) => (
            <div key={advantage.id} className="bg-[#F5F5F5] rounded-lg p-6 hover:shadow-lg transition-shadow duration-300">
              {/* Icon */}
              <div className="bg-white rounded-lg w-16 h-16 flex items-center justify-center mb-6 shadow-sm">
                {getIcon(advantage.icon)}
              </div>

              {/* Content */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-[#262626]">{advantage.title}</h3>
                <p className="text-gray-600 leading-relaxed">
                  {advantage.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
