import { Link } from 'react-router-dom';
import { useState } from 'react';

type FooterProps = {
  className?: string;
};

export default function Footer({ className = '' }: FooterProps) {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <footer className={`relative bg-[#F5F5F5] overflow-hidden ${className}`}>
      <div className="w-full max-w-[1440px] h-[474px] mx-auto relative">
        {/* Main Title */}
        <div className="absolute left-[736px] top-[40px] justify-start text-[#262626] text-4xl font-normal font-['Roboto'] leading-10 tracking-tight">
          We're always happy to help
        </div>

        {/* Navigation Links */}
        <div className="absolute left-[504px] top-[194px] inline-flex flex-col justify-start items-start gap-3">
          <div data-size="Small" data-state="Default" className="relative inline-flex justify-start items-center gap-2.5">
            <Link to="/projects" className="justify-start text-[#656CAF] text-base font-bold font-['Roboto'] leading-normal tracking-tight hover:text-[#4C53A2] transition-colors">
              Projects
            </Link>
            <div className="w-px h-0.5 left-0 top-[19px] absolute opacity-0 bg-[#656CAF]/20"></div>
          </div>
          <div data-size="Small" data-state="Default" className="relative inline-flex justify-start items-center gap-2.5">
            <Link to="/about" className="justify-start text-[#656CAF] text-base font-bold font-['Roboto'] leading-normal tracking-tight hover:text-[#4C53A2] transition-colors">
              About Us
            </Link>
            <div className="w-px h-0.5 left-0 top-[19px] absolute opacity-0 bg-[#656CAF]/20"></div>
          </div>
          <div data-size="Small" data-state="Default" className="relative inline-flex justify-start items-center gap-2.5">
            <Link to="/articles" className="justify-start text-[#656CAF] text-base font-bold font-['Roboto'] leading-normal tracking-tight hover:text-[#4C53A2] transition-colors">
              Articles
            </Link>
            <div className="w-px h-0.5 left-0 top-[19px] absolute opacity-0 bg-[#656CAF]/20"></div>
          </div>
          <div data-size="Small" data-state="Default" className="relative inline-flex justify-start items-center gap-2.5">
            <Link to="/manifestos" className="justify-start text-[#4C53A2] text-base font-bold font-['Roboto'] leading-normal tracking-tight hover:text-[#656CAF] transition-colors">
              Manifestos
            </Link>
            <div className="w-px h-0.5 left-0 top-[19px] absolute opacity-0 bg-[#656CAF]/20"></div>
          </div>
          <div data-size="Small" data-state="Default" className="relative inline-flex justify-start items-center gap-2.5">
            <Link to="/career" className="justify-start text-[#656CAF] text-base font-bold font-['Roboto'] leading-normal tracking-tight hover:text-[#4C53A2] transition-colors">
              Career
            </Link>
            <div className="w-px h-0.5 left-0 top-[19px] absolute opacity-0 bg-[#656CAF]/20"></div>
          </div>
          <div data-size="Small" data-state="Default" className="relative inline-flex justify-start items-center gap-2.5">
            <Link to="/privacy" className="justify-start text-[#656CAF] text-base font-bold font-['Roboto'] leading-normal tracking-tight hover:text-[#4C53A2] transition-colors">
              Privacy policy
            </Link>
            <div className="w-px h-0.5 left-0 top-[19px] absolute opacity-0 bg-[#656CAF]/20"></div>
          </div>
          <div data-size="Small" data-state="Default" className="relative inline-flex justify-start items-center gap-2.5">
            <Link to="/cookies" className="justify-start text-[#656CAF] text-base font-bold font-['Roboto'] leading-normal tracking-tight hover:text-[#4C53A2] transition-colors">
              Cookie policy
            </Link>
            <div className="w-px h-0.5 left-0 top-[19px] absolute opacity-0 bg-[#656CAF]/20"></div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="w-80 absolute left-[40px] top-[194px] inline-flex flex-col justify-end items-start gap-6">
          <div className="justify-start">
            <span className="text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">the part of </span>
            <span className="text-[#262626] text-sm font-bold font-['Roboto'] leading-none tracking-tight">Expoglobal Group</span>
          </div>

          {/* Social Media Icons */}
          <div className="inline-flex justify-start items-center gap-2">
            <a href="#" className="w-6 h-6 relative overflow-hidden hover:opacity-75 transition-opacity">
              <div className="w-1.5 h-1.5 absolute left-[8.67px] top-[8.67px] bg-[#262626]"></div>
              <div className="w-5 h-5 absolute left-[2px] top-[2px] bg-[#262626]"></div>
            </a>
            <a href="#" className="w-6 h-6 relative overflow-hidden hover:opacity-75 transition-opacity">
              <div className="w-5 h-5 absolute left-[2px] top-[2px] bg-[#262626]"></div>
            </a>
            <a href="#" className="w-6 h-6 relative overflow-hidden hover:opacity-75 transition-opacity">
              <div className="w-5 h-5 absolute left-[2px] top-[2px] bg-[#262626]"></div>
            </a>
            <a href="#" className="w-6 h-6 relative overflow-hidden hover:opacity-75 transition-opacity">
              <div className="w-5 h-5 absolute left-[2px] top-[2px] bg-[#262626]"></div>
            </a>
          </div>

          {/* Phone */}
          <div className="inline-flex justify-start items-center gap-1">
            <div className="w-6 h-6 relative overflow-hidden"></div>
            <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">+971 4 548 5887</div>
          </div>

          {/* Email */}
          <div className="inline-flex justify-start items-center gap-1">
            <div className="w-6 h-6 relative overflow-hidden"></div>
            <div className="justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight"><EMAIL></div>
          </div>

          {/* Location */}
          <div className="self-stretch inline-flex justify-start items-start gap-1">
            <div className="w-6 h-6 relative overflow-hidden"></div>
            <div className="flex-1 justify-start text-[#262626] text-sm font-normal font-['Roboto'] leading-none tracking-tight">
              UAE, Dubai, Dubai Industrial City, KJ Autopart complex, Office building, ground floor, left wing. PO box 118995
            </div>
          </div>
        </div>

        {/* Logo */}
        <img
          className="w-40 h-20 absolute left-[40px] top-[40px]"
          src="/images/messe-logo.png"
          alt="Messe.ae Logo"
        />

        {/* Logo Text */}
        <div className="w-40 absolute left-[40px] top-[140px] justify-start">
          <span className="text-[#656CAF] text-xs font-bold font-['Roboto'] leading-3">M</span>
          <span className="text-[#262626] text-xs font-medium font-['Roboto'] leading-3">arketing and </span>
          <span className="text-[#656CAF] text-xs font-bold font-['Roboto'] leading-3">E</span>
          <span className="text-[#262626] text-xs font-medium font-['Roboto'] leading-3">xhibition </span>
          <span className="text-[#656CAF] text-xs font-bold font-['Roboto'] leading-3">S</span>
          <span className="text-[#262626] text-xs font-medium font-['Roboto'] leading-3">ervices</span>
        </div>

        {/* Contact Form */}
        <div className="w-[664px] px-8 py-6 absolute left-[736px] top-[106px] bg-white rounded-lg inline-flex flex-col justify-start items-start gap-5 overflow-hidden">
          <div className="self-stretch justify-start text-[#262626] text-base font-bold font-['Roboto'] leading-normal tracking-tight">
            Have a project to discuss? Fill the form below and our experts will contact you within 24 hours.
          </div>

          <form onSubmit={handleSubmit} className="self-stretch flex flex-col justify-start items-start gap-5">
            <div className="self-stretch flex flex-col justify-start items-start gap-3">
              {/* Name Field */}
              <div className="self-stretch flex flex-col justify-start items-start">
                <div className="self-stretch px-3 rounded outline outline-1 outline-offset-[-1px] outline-[#C4C4C4] flex flex-col justify-start items-start">
                  <div className="self-stretch min-h-6 py-2 inline-flex justify-start items-center overflow-hidden">
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="flex-1 bg-transparent border-none outline-none text-[#262626] text-base font-normal font-['Roboto'] leading-normal tracking-tight"
                      placeholder=""
                    />
                    <div className="flex-1 justify-start">
                      <span className="text-[#7B7B7B] text-base font-normal font-['Roboto'] leading-normal tracking-tight">Name</span>
                      <span className="text-red-500 text-base font-normal font-['Roboto'] leading-normal tracking-tight">*</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Phone Field */}
              <div className="self-stretch flex flex-col justify-start items-start">
                <div className="self-stretch px-3 rounded outline outline-1 outline-offset-[-1px] outline-[#C4C4C4] flex flex-col justify-start items-start">
                  <div className="self-stretch min-h-6 py-2 inline-flex justify-start items-center overflow-hidden">
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      required
                      className="flex-1 bg-transparent border-none outline-none text-[#262626] text-base font-normal font-['Roboto'] leading-normal tracking-tight"
                      placeholder=""
                    />
                    <div className="flex-1 justify-start">
                      <span className="text-[#7B7B7B] text-base font-normal font-['Roboto'] leading-normal tracking-tight">Phone</span>
                      <span className="text-red-500 text-base font-normal font-['Roboto'] leading-normal tracking-tight">*</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Email Field */}
              <div className="self-stretch flex flex-col justify-start items-start">
                <div className="self-stretch px-3 rounded outline outline-1 outline-offset-[-1px] outline-[#C4C4C4] flex flex-col justify-start items-start">
                  <div className="self-stretch min-h-6 py-2 inline-flex justify-start items-center overflow-hidden">
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="flex-1 bg-transparent border-none outline-none text-[#262626] text-base font-normal font-['Roboto'] leading-normal tracking-tight"
                      placeholder=""
                    />
                    <div className="flex-1 justify-start">
                      <span className="text-[#7B7B7B] text-base font-normal font-['Roboto'] leading-normal tracking-tight">Email</span>
                      <span className="text-red-500 text-base font-normal font-['Roboto'] leading-normal tracking-tight">*</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              className="self-stretch h-12 px-5 py-2 bg-[#656CAF] rounded-lg shadow-[0px_3px_1px_-2px_rgba(0,0,0,0.20)] shadow-[0px_2px_2px_0px_rgba(0,0,0,0.14)] shadow-[0px_1px_5px_0px_rgba(0,0,0,0.12)] flex flex-col justify-center items-center overflow-hidden hover:bg-[#4C53A2] transition-colors"
            >
              <div className="inline-flex justify-center items-center gap-2">
                <div className="justify-start text-white text-2xl font-normal font-['Roboto'] leading-7 tracking-tight">Send</div>
              </div>
            </button>
          </form>
        </div>
      </div>
    </footer>
  );
}
