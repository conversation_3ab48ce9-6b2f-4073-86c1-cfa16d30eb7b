import { useState } from 'react';

type ContactFormProps = {
  className?: string;
};

export default function ContactForm({ className = '' }: ContactFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    comments: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="max-w-container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Form */}
          <div>
            <h2 className="text-5xl font-bold text-black mb-8">
              We're always happy to help
            </h2>
            
            <div className="bg-background rounded-custom p-8">
              <p className="text-xl font-bold text-black mb-8">
                Have a project to discuss? Fill the form below and our<br />
                experts will contact you within 24 hours.
              </p>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Field */}
                <div>
                  <label htmlFor="name" className="block text-base font-medium text-black mb-2">
                    Name*
                  </label>
                  <input
                    id="name"
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full bg-white rounded-custom px-4 py-3 border-none focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>

                {/* Phone Field */}
                <div>
                  <label htmlFor="phone" className="block text-base font-medium text-black mb-2">
                    Phone*
                  </label>
                  <input
                    id="phone"
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    className="w-full bg-white rounded-custom px-4 py-3 border-none focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>

                {/* Comments Field */}
                <div>
                  <label htmlFor="comments" className="block text-base font-medium text-black mb-2">
                    Comments
                  </label>
                  <textarea
                    id="comments"
                    name="comments"
                    value={formData.comments}
                    onChange={handleChange}
                    rows={4}
                    className="w-full bg-white rounded-custom px-4 py-3 border-none focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                  />
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  className="w-full border-2 border-black text-black font-bold text-xl px-10 py-5 rounded-custom hover:bg-black hover:text-white transition-all duration-300 uppercase"
                >
                  get a callback
                </button>
              </form>
            </div>
          </div>

          {/* Right Side - Images */}
          <div className="space-y-6">
            <div className="bg-gray-custom rounded-custom h-64"></div>
            <div className="bg-gray-custom rounded-custom h-32"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
