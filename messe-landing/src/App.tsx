import { Routes, Route } from 'react-router-dom'
import Home from './pages/Home'
import AboutUs from './pages/AboutUs'
import Projects from './pages/Projects'
import Chatbot from './components/Chatbot'
import ChatButton from './components/ChatButton'
import { useChatbot } from './hooks/useChatbot'

function App() {
  const { isOpen, messages, isTyping, openChat, closeChat, sendMessage } = useChatbot();

  return (
    <>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<AboutUs />} />
        <Route path="/projects" element={<Projects />} />
      </Routes>

      {/* Chat components */}
      <ChatButton onClick={openChat} isOpen={isOpen} />
      <Chatbot
        isOpen={isOpen}
        messages={messages}
        isTyping={isTyping}
        onClose={closeChat}
        onSendMessage={sendMessage}
      />
    </>
  )
}

export default App
