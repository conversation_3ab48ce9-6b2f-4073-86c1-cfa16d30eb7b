import { render, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import ChatButton from '../components/ChatButton'

describe('ChatButton', () => {
  beforeEach(() => {
    // Clear DOM before each test
    document.body.innerHTML = '';
  });

  afterEach(() => {
    // Clean up after each test
    document.body.innerHTML = '';
  });

  it('renders chat button when closed', () => {
    const onClick = vi.fn();
    const { container } = render(<ChatButton onClick={onClick} isOpen={false} />);

    const button = container.querySelector('button[aria-label="Open chat"]');
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('scale-100', 'opacity-100');
  });

  it('hides when chat is open', () => {
    const onClick = vi.fn();
    const { container } = render(<ChatButton onClick={onClick} isOpen={true} />);

    const button = container.querySelector('button[aria-label="Open chat"]');
    expect(button).toHaveClass('scale-0', 'opacity-0');
  });

  it('calls onClick when clicked', () => {
    const onClick = vi.fn();
    const { container } = render(<ChatButton onClick={onClick} isOpen={false} />);

    const button = container.querySelector('button[aria-label="Open chat"]');
    fireEvent.click(button!);

    expect(onClick).toHaveBeenCalled();
  });

  it('has notification dot', () => {
    const onClick = vi.fn();
    const { container } = render(<ChatButton onClick={onClick} isOpen={false} />);

    const button = container.querySelector('button[aria-label="Open chat"]');
    const notificationDot = button!.querySelector('.animate-pulse');
    expect(notificationDot).toBeInTheDocument();
  });
});
