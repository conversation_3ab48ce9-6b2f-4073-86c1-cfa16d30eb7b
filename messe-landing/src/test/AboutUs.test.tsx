import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { BrowserRouter } from 'react-router-dom'
import AboutUs from '../pages/AboutUs'

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('AboutUs', () => {
  it('renders about us page sections', () => {
    renderWithRouter(<AboutUs />)

    // Check if main sections are rendered using more specific selectors
    expect(screen.getByRole('heading', { name: /about us/i, level: 1 })).toBeInTheDocument()
    expect(screen.getByRole('heading', { name: /our story/i })).toBeInTheDocument()
    expect(screen.getByRole('heading', { name: /our services/i })).toBeInTheDocument()
    expect(screen.getByRole('heading', { name: /expoglobal group/i })).toBeInTheDocument()
  })

  it('renders service cards', () => {
    renderWithRouter(<AboutUs />)

    // Check that we have 6 service cards
    const serviceCards = screen.getAllByText(/Exhibition Stand Design|Construction & Installation|Project Management|Pavilion Design|Retail Spaces|Bespoke Exhibits/)
    expect(serviceCards.length).toBeGreaterThanOrEqual(6)
  })

  it('renders timeline events', () => {
    renderWithRouter(<AboutUs />)

    // Check for unique timeline event descriptions using getAllByText
    const companyFoundedElements = screen.getAllByText('Company Founded')
    expect(companyFoundedElements.length).toBeGreaterThan(0)

    const aiIntegrationElements = screen.getAllByText('AI Integration')
    expect(aiIntegrationElements.length).toBeGreaterThan(0)
  })

  it('renders contact form', () => {
    renderWithRouter(<AboutUs />)
    
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/comments/i)).toBeInTheDocument()
  })
})
