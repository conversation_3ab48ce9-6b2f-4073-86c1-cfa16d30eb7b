import { render, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import Chatbot from '../components/Chatbot'
import type { Message } from '../hooks/useChatbot'

// Mock scrollIntoView
beforeEach(() => {
  Element.prototype.scrollIntoView = vi.fn()
})

const mockMessages: Message[] = [
  {
    id: 'welcome',
    text: 'Здравствуйте! Как дела?',
    isUser: false,
    timestamp: new Date()
  }
];

describe('Chatbot', () => {
  const defaultProps = {
    isOpen: true,
    messages: mockMessages,
    isTyping: false,
    onClose: vi.fn(),
    onSendMessage: vi.fn()
  };

  beforeEach(() => {
    // Clear DOM before each test
    document.body.innerHTML = '';
  });

  afterEach(() => {
    // Clean up after each test
    document.body.innerHTML = '';
  });

  it('renders when open', () => {
    const { container } = render(<Chatbot {...defaultProps} />);

    expect(container.querySelector('h3')).toHaveTextContent('Поддержка');
    expect(container.querySelector('p')).toHaveTextContent('Онлайн');
    expect(container).toHaveTextContent('Здравствуйте! Как дела?');
  });

  it('does not render when closed', () => {
    const { container } = render(<Chatbot {...defaultProps} isOpen={false} />);

    expect(container.querySelector('h3')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const onClose = vi.fn();
    const { container } = render(<Chatbot {...defaultProps} onClose={onClose} />);

    const closeButton = container.querySelector('button[aria-label="Close chat"]');
    fireEvent.click(closeButton!);

    expect(onClose).toHaveBeenCalled();
  });

  it('sends message when form is submitted', async () => {
    const onSendMessage = vi.fn();
    const { container } = render(<Chatbot {...defaultProps} onSendMessage={onSendMessage} />);

    const input = container.querySelector('input[placeholder="Введите сообщение..."]') as HTMLInputElement;
    const sendButton = container.querySelector('button[aria-label="Send message"]');

    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton!);

    expect(onSendMessage).toHaveBeenCalledWith('Test message');
  });

  it('shows typing indicator when isTyping is true', () => {
    const { container } = render(<Chatbot {...defaultProps} isTyping={true} />);

    // Check for animated dots (typing indicator)
    const typingDots = container.querySelectorAll('.animate-bounce');
    expect(typingDots.length).toBeGreaterThan(0);
  });

  it('disables input when typing', () => {
    const { container } = render(<Chatbot {...defaultProps} isTyping={true} />);

    const input = container.querySelector('input[placeholder="Введите сообщение..."]') as HTMLInputElement;
    expect(input).toBeDisabled();
  });
});
