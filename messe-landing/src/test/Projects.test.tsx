import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { <PERSON>rowserRouter } from 'react-router-dom'
import Projects from '../pages/Projects'

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('Projects', () => {
  it('renders projects page sections', () => {
    renderWithRouter(<Projects />)

    // Check if main sections are rendered
    expect(screen.getByRole('heading', { name: /our projects/i, level: 1 })).toBeInTheDocument()
    expect(screen.getByRole('heading', { name: /industries/i })).toBeInTheDocument()

    // Use getAllByText since "We're always happy to help" appears multiple times now
    const helpTexts = screen.getAllByText("We're always happy to help")
    expect(helpTexts.length).toBeGreaterThan(0)
  })

  it('renders project cards', () => {
    renderWithRouter(<Projects />)

    // Check that we have project cards
    const projectTitles = screen.getAllByText(/Tech Expo 2024|Healthcare Summit|Auto Show Dubai|Fashion Week/)
    expect(projectTitles.length).toBeGreaterThanOrEqual(4)
  })

  it('renders project details', () => {
    renderWithRouter(<Projects />)

    // Check for project clients and areas using getAllByText
    const techCorpElements = screen.getAllByText('TechCorp')
    expect(techCorpElements.length).toBeGreaterThan(0)

    const areaElements = screen.getAllByText('120 m²')
    expect(areaElements.length).toBeGreaterThan(0)
  })

  it('renders industry cards', () => {
    renderWithRouter(<Projects />)

    // Check that we have 8 industry cards
    const industryCards = screen.getAllByText(/Technology|Healthcare|Automotive|Fashion|Food & Beverage|Real Estate|Energy|Education/)
    expect(industryCards.length).toBeGreaterThanOrEqual(8)
  })

  it('renders contact form', () => {
    renderWithRouter(<Projects />)

    // Form is now in footer with placeholder text instead of labels
    const nameInputs = screen.getAllByPlaceholderText(/name/i)
    expect(nameInputs.length).toBeGreaterThan(0)

    const phoneInputs = screen.getAllByPlaceholderText(/phone/i)
    expect(phoneInputs.length).toBeGreaterThan(0)

    const emailInputs = screen.getAllByPlaceholderText(/email/i)
    expect(emailInputs.length).toBeGreaterThan(0)

    const sendButtons = screen.getAllByRole('button', { name: /send/i })
    expect(sendButtons.length).toBeGreaterThan(0)
  })
})
