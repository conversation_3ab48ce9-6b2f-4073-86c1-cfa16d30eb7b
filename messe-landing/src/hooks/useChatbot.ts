import { useState, useCallback } from 'react';

export type Message = {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
};

type UseChatbotReturn = {
  isOpen: boolean;
  messages: Message[];
  isTyping: boolean;
  openChat: () => void;
  closeChat: () => void;
  sendMessage: (text: string) => void;
};

const AUTO_RESPONSES = [
  "Спасибо за ваше сообщение! Наш менеджер свяжется с вами в ближайшее время.",
  "Мы получили ваш запрос. Обычно отвечаем в течение 15 минут в рабочее время.",
  "Отличный вопрос! Позвольте мне найти для вас подробную информацию.",
  "Я передам ваш запрос нашим специалистам. Ожидайте ответа в ближайшее время.",
  "Благодарим за интерес к нашим услугам! Мы обязательно с вами свяжемся."
];

const INITIAL_MESSAGE: Message = {
  id: 'welcome',
  text: 'Здравствуйте! Я помогу вам с вопросами о наших проектах и услугах. Как дела?',
  isUser: false,
  timestamp: new Date()
};

export const useChatbot = (): UseChatbotReturn => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([INITIAL_MESSAGE]);
  const [isTyping, setIsTyping] = useState(false);

  const openChat = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeChat = useCallback(() => {
    setIsOpen(false);
  }, []);

  const sendMessage = useCallback((text: string) => {
    if (!text.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      text: text.trim(),
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsTyping(true);

    // Simulate bot response with delay
    setTimeout(() => {
      const randomResponse = AUTO_RESPONSES[Math.floor(Math.random() * AUTO_RESPONSES.length)];
      const botMessage: Message = {
        id: `bot-${Date.now()}`,
        text: randomResponse,
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000); // Random delay between 1-3 seconds
  }, []);

  return {
    isOpen,
    messages,
    isTyping,
    openChat,
    closeChat,
    sendMessage
  };
};
