@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;800&display=swap');
@import "tailwindcss";

@theme {
  --color-primary: #656CAF;
  --color-background: #E9E9E9;
  --color-gray-custom: #D9D9D9;
  --color-gray-text: #6F6F6F;

  --font-family-inter: 'Inter', sans-serif;

  --max-width-container: 1360px;
  --border-radius-custom: 8px;
}

body {
  font-family: var(--font-family-inter);
}

/* Client logos animations */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-reverse {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll-reverse {
  animation: scroll-reverse 30s linear infinite;
}
